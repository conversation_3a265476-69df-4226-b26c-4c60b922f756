import { Controller, UseGuards } from "@nestjs/common";
import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { RolesGuard } from "../roles/roles.guard";
import { Roles } from "../roles/roles.decorator";
import { SystemLogsService } from "./systemlogs.service";

@Controller("systemlogs")
@UseGuards(JwtAuthGuard, RolesGuard)
export class SystemLogsController {
  constructor(private readonly systemLogsService: SystemLogsService) {}

  @Roles("admin", "system-emp")
  @Post()
  @HttpCode(HttpStatus.CREATED)
  async createSystemLog(@Body() systemLog: SystemLogsDto): Promise<void> {
    return this.systemLogsService.createSystemLog(systemLog);
  }

}